/**
 * 瞬光捕手 - 用户管理界面样式
 * 为新的用户管理系统提供现代化的界面样式
 */

/* 用户切换按钮 */
.user-switch-container {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.user-switch-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.user-switch-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.user-switch-btn .user-icon {
    font-size: 16px;
}

.user-switch-btn .switch-icon {
    font-size: 12px;
    opacity: 0.8;
}

/* 用户信息显示 */
.user-info-display {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
}

.user-info-compact {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 15px;
    font-size: 12px;
    backdrop-filter: blur(10px);
}

.user-name-display {
    font-weight: 500;
}

.user-type-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.user-type-badge.registered {
    background: #4CAF50;
    color: white;
}

.user-type-badge.guest {
    background: #FF9800;
    color: white;
}

.user-type-badge.anonymous {
    background: #9E9E9E;
    color: white;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 30px;
}

/* 用户信息卡片 */
.user-info-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.user-identifier {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 8px;
    font-family: monospace;
}

.user-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    opacity: 0.9;
}

.stat-item {
    padding: 2px 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
}

/* 用户列表 */
.user-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.user-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.user-list-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.user-list-item.current {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.user-item-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.user-item-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.user-item-details {
    flex: 1;
}

.user-item-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.user-item-identifier {
    font-size: 12px;
    opacity: 0.8;
    font-family: monospace;
    margin-bottom: 6px;
}

.user-item-meta {
    display: flex;
    gap: 10px;
    align-items: center;
}

.user-last-active {
    font-size: 11px;
    opacity: 0.7;
}

.user-item-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.current-badge {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* 表单样式 */
.create-user-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group small {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    opacity: 0.8;
}

.form-actions {
    margin-top: 20px;
}

/* 按钮样式 */
.primary-btn, .action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.primary-btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.primary-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    margin: 0 5px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.switch-btn {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.edit-btn {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.delete-btn {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* 动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .user-list-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .user-item-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .user-switch-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .user-switch-btn .user-display {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .modal-content {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    }
    
    .user-info-card,
    .user-list-item,
    .create-user-form {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .modal-content {
        border: 2px solid white;
    }
    
    .user-switch-btn,
    .primary-btn,
    .action-btn {
        border: 1px solid white;
    }
    
    .user-type-badge {
        border: 1px solid currentColor;
    }
}
