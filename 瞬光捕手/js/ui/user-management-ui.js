/**
 * 瞬光捕手 - 用户管理界面
 * 提供用户创建、切换、管理的图形界面
 */

class UserManagementUI {
    constructor() {
        this.initialized = false;
        this.userManager = null;
        this.userSwitchManager = null;
        this.currentModal = null;
        
        console.log('🎨 用户管理界面已创建');
    }

    /**
     * 初始化用户管理界面
     */
    async init() {
        try {
            // 获取依赖服务
            this.userManager = window.userManager;
            this.userSwitchManager = window.userSwitchManager;
            
            if (!this.userManager) {
                throw new Error('用户管理器未找到');
            }

            // 创建界面元素
            this.createUserManagementModal();
            this.createUserSwitchButton();
            this.createUserInfoDisplay();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 更新界面显示
            this.updateUserDisplay();
            
            this.initialized = true;
            console.log('✅ 用户管理界面初始化完成');
            
        } catch (error) {
            console.error('❌ 用户管理界面初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建用户管理模态框
     */
    createUserManagementModal() {
        const modalHtml = `
            <div id="user-management-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>👤 用户管理</h2>
                        <button class="modal-close" onclick="userManagementUI.hideModal()">&times;</button>
                    </div>
                    
                    <div class="modal-body">
                        <!-- 当前用户信息 -->
                        <div class="current-user-section">
                            <h3>当前用户</h3>
                            <div id="current-user-info" class="user-info-card">
                                <div class="user-avatar">👤</div>
                                <div class="user-details">
                                    <div class="user-name">未知用户</div>
                                    <div class="user-identifier">unknown</div>
                                    <div class="user-stats">
                                        <span class="stat-item">创建时间: --</span>
                                        <span class="stat-item">最后活跃: --</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户列表 -->
                        <div class="user-list-section">
                            <h3>所有用户</h3>
                            <div id="user-list" class="user-list">
                                <!-- 用户列表将在这里动态生成 -->
                            </div>
                        </div>

                        <!-- 创建新用户 -->
                        <div class="create-user-section">
                            <h3>创建新用户</h3>
                            <div class="create-user-form">
                                <div class="form-group">
                                    <label for="new-user-identifier">用户标识符:</label>
                                    <input type="text" id="new-user-identifier" 
                                           placeholder="输入唯一的用户标识符" 
                                           maxlength="32" 
                                           pattern="[a-zA-Z0-9_-]+"
                                           title="只允许字母、数字、下划线和连字符">
                                    <small>用于系统内部识别，创建后不可修改</small>
                                </div>
                                <div class="form-group">
                                    <label for="new-user-display-name">显示名称:</label>
                                    <input type="text" id="new-user-display-name" 
                                           placeholder="输入显示名称" 
                                           maxlength="50">
                                    <small>其他用户看到的名称，可以随时修改</small>
                                </div>
                                <div class="form-actions">
                                    <button id="create-user-btn" class="primary-btn" onclick="userManagementUI.handleCreateUser()">
                                        创建用户
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 用户操作 -->
                        <div class="user-actions-section">
                            <h3>用户操作</h3>
                            <div class="action-buttons">
                                <button class="action-btn" onclick="userManagementUI.exportCurrentUser()">
                                    📤 导出当前用户数据
                                </button>
                                <button class="action-btn" onclick="userManagementUI.showImportDialog()">
                                    📥 导入用户数据
                                </button>
                                <button class="action-btn" onclick="userManagementUI.showDataStats()">
                                    📊 查看数据统计
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    /**
     * 创建用户切换按钮
     */
    createUserSwitchButton() {
        // 查找合适的位置插入用户切换按钮
        const gameHeader = document.querySelector('.game-header') || document.querySelector('header');
        
        if (gameHeader) {
            const userSwitchHtml = `
                <div id="user-switch-container" class="user-switch-container">
                    <button id="user-switch-btn" class="user-switch-btn" onclick="userManagementUI.showModal()">
                        <span class="user-icon">👤</span>
                        <span id="current-user-display" class="user-display">游客</span>
                        <span class="switch-icon">⚙️</span>
                    </button>
                </div>
            `;
            
            gameHeader.insertAdjacentHTML('beforeend', userSwitchHtml);
        }
    }

    /**
     * 创建用户信息显示
     */
    createUserInfoDisplay() {
        // 在游戏界面中添加用户信息显示区域
        const gameUI = document.querySelector('.game-ui') || document.querySelector('.ui-container');
        
        if (gameUI) {
            const userInfoHtml = `
                <div id="user-info-display" class="user-info-display">
                    <div class="user-info-compact">
                        <span class="user-name-display" id="user-name-display">游客</span>
                        <span class="user-type-badge" id="user-type-badge">guest</span>
                    </div>
                </div>
            `;
            
            gameUI.insertAdjacentHTML('afterbegin', userInfoHtml);
        }
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听用户管理器事件
        if (this.userManager) {
            this.userManager.addEventListener('userSwitched', (eventData) => {
                this.updateUserDisplay();
                this.updateUserList();
            });
            
            this.userManager.addEventListener('userCreated', (eventData) => {
                this.updateUserList();
                this.showSuccessMessage(`用户创建成功: ${eventData.user.displayName}`);
            });
            
            this.userManager.addEventListener('userDeleted', (eventData) => {
                this.updateUserList();
                this.showSuccessMessage(`用户删除成功: ${eventData.identifier}`);
            });
        }

        // 监听用户切换事件
        window.addEventListener('userSwitch:completed', (event) => {
            this.showSuccessMessage(`切换到用户: ${event.detail.currentUser.displayName}`);
        });

        window.addEventListener('userSwitch:failed', (event) => {
            this.showErrorMessage(`用户切换失败: ${event.detail.error}`);
        });

        // 监听键盘事件
        document.addEventListener('keydown', (event) => {
            // Ctrl+U 快速打开用户管理
            if (event.ctrlKey && event.key === 'u') {
                event.preventDefault();
                this.showModal();
            }
            
            // ESC 关闭模态框
            if (event.key === 'Escape' && this.currentModal) {
                this.hideModal();
            }
        });
    }

    /**
     * 显示用户管理模态框
     */
    showModal() {
        const modal = document.getElementById('user-management-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.currentModal = modal;
            this.updateCurrentUserInfo();
            this.updateUserList();
        }
    }

    /**
     * 隐藏用户管理模态框
     */
    hideModal() {
        const modal = document.getElementById('user-management-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.currentModal = null;
        }
    }

    /**
     * 更新用户显示
     */
    updateUserDisplay() {
        const currentUser = this.userManager.getCurrentUser();
        
        // 更新切换按钮显示
        const userDisplay = document.getElementById('current-user-display');
        if (userDisplay && currentUser) {
            userDisplay.textContent = currentUser.displayName;
        }

        // 更新用户信息显示
        const userNameDisplay = document.getElementById('user-name-display');
        const userTypeBadge = document.getElementById('user-type-badge');
        
        if (userNameDisplay && currentUser) {
            userNameDisplay.textContent = currentUser.displayName;
        }
        
        if (userTypeBadge && currentUser) {
            userTypeBadge.textContent = currentUser.type;
            userTypeBadge.className = `user-type-badge ${currentUser.type}`;
        }
    }

    /**
     * 更新当前用户信息
     */
    updateCurrentUserInfo() {
        const currentUser = this.userManager.getCurrentUser();
        const userInfoElement = document.getElementById('current-user-info');

        if (userInfoElement && currentUser) {
            const userNameElement = userInfoElement.querySelector('.user-name');
            const userIdentifierElement = userInfoElement.querySelector('.user-identifier');
            const userStatsElement = userInfoElement.querySelector('.user-stats');

            if (userNameElement) {
                userNameElement.textContent = currentUser.displayName;
            }

            if (userIdentifierElement) {
                userIdentifierElement.textContent = currentUser.identifier;
            }

            if (userStatsElement) {
                const createdDate = new Date(currentUser.createdAt).toLocaleDateString();
                const lastActiveDate = new Date(currentUser.lastActiveAt).toLocaleDateString();

                userStatsElement.innerHTML = `
                    <span class="stat-item">创建时间: ${createdDate}</span>
                    <span class="stat-item">最后活跃: ${lastActiveDate}</span>
                `;
            }
        }
    }

    /**
     * 更新用户列表
     */
    updateUserList() {
        const userListElement = document.getElementById('user-list');
        if (!userListElement) return;

        const users = this.userManager.getAllUsers();
        const currentUser = this.userManager.getCurrentUser();

        userListElement.innerHTML = '';

        users.forEach(user => {
            const userItem = this.createUserListItem(user, currentUser);
            userListElement.appendChild(userItem);
        });
    }

    /**
     * 创建用户列表项
     * @param {Object} user - 用户对象
     * @param {Object} currentUser - 当前用户对象
     * @returns {HTMLElement} 用户列表项元素
     */
    createUserListItem(user, currentUser) {
        const isCurrentUser = currentUser && currentUser.identifier === user.identifier;

        const userItem = document.createElement('div');
        userItem.className = `user-list-item ${isCurrentUser ? 'current' : ''}`;

        userItem.innerHTML = `
            <div class="user-item-info">
                <div class="user-item-avatar">👤</div>
                <div class="user-item-details">
                    <div class="user-item-name">${user.displayName}</div>
                    <div class="user-item-identifier">${user.identifier}</div>
                    <div class="user-item-meta">
                        <span class="user-type-badge ${user.type}">${user.type}</span>
                        <span class="user-last-active">最后活跃: ${new Date(user.lastActiveAt).toLocaleDateString()}</span>
                    </div>
                </div>
            </div>
            <div class="user-item-actions">
                ${!isCurrentUser ? `
                    <button class="action-btn switch-btn" onclick="userManagementUI.switchToUser('${user.identifier}')">
                        切换
                    </button>
                ` : '<span class="current-badge">当前用户</span>'}

                ${user.type !== 'guest' ? `
                    <button class="action-btn edit-btn" onclick="userManagementUI.editUser('${user.identifier}')">
                        编辑
                    </button>
                ` : ''}

                ${user.type !== 'guest' && !isCurrentUser ? `
                    <button class="action-btn delete-btn" onclick="userManagementUI.deleteUser('${user.identifier}')">
                        删除
                    </button>
                ` : ''}
            </div>
        `;

        return userItem;
    }

    /**
     * 处理创建用户
     */
    async handleCreateUser() {
        const identifierInput = document.getElementById('new-user-identifier');
        const displayNameInput = document.getElementById('new-user-display-name');

        if (!identifierInput || !displayNameInput) {
            this.showErrorMessage('表单元素未找到');
            return;
        }

        const identifier = identifierInput.value.trim();
        const displayName = displayNameInput.value.trim();

        if (!identifier || !displayName) {
            this.showErrorMessage('请填写完整的用户信息');
            return;
        }

        try {
            // 显示创建中状态
            const createBtn = document.getElementById('create-user-btn');
            if (createBtn) {
                createBtn.disabled = true;
                createBtn.textContent = '创建中...';
            }

            // 创建用户
            await this.userManager.createUser(identifier, displayName);

            // 清空表单
            identifierInput.value = '';
            displayNameInput.value = '';

            // 恢复按钮状态
            if (createBtn) {
                createBtn.disabled = false;
                createBtn.textContent = '创建用户';
            }

        } catch (error) {
            this.showErrorMessage(`创建用户失败: ${error.message}`);

            // 恢复按钮状态
            const createBtn = document.getElementById('create-user-btn');
            if (createBtn) {
                createBtn.disabled = false;
                createBtn.textContent = '创建用户';
            }
        }
    }

    /**
     * 切换到指定用户
     * @param {string} identifier - 用户标识符
     */
    async switchToUser(identifier) {
        try {
            if (this.userSwitchManager) {
                await this.userSwitchManager.switchUser(identifier);
            } else {
                await this.userManager.switchToUser(identifier);
            }

            // 关闭模态框
            this.hideModal();

        } catch (error) {
            this.showErrorMessage(`切换用户失败: ${error.message}`);
        }
    }

    /**
     * 编辑用户
     * @param {string} identifier - 用户标识符
     */
    async editUser(identifier) {
        const user = this.userManager.users.get(identifier);
        if (!user) {
            this.showErrorMessage('用户不存在');
            return;
        }

        const newDisplayName = prompt('请输入新的显示名称:', user.displayName);
        if (newDisplayName && newDisplayName.trim() !== user.displayName) {
            try {
                await this.userManager.updateUserDisplayName(identifier, newDisplayName.trim());
                this.updateUserList();
                this.updateUserDisplay();
            } catch (error) {
                this.showErrorMessage(`更新显示名称失败: ${error.message}`);
            }
        }
    }

    /**
     * 删除用户
     * @param {string} identifier - 用户标识符
     */
    async deleteUser(identifier) {
        const user = this.userManager.users.get(identifier);
        if (!user) {
            this.showErrorMessage('用户不存在');
            return;
        }

        const confirmMessage = `确定要删除用户 "${user.displayName}" (${identifier}) 吗？\n\n此操作将永久删除该用户的所有数据，且无法撤销。`;

        if (confirm(confirmMessage)) {
            try {
                await this.userManager.deleteUser(identifier);
                this.updateUserList();
            } catch (error) {
                this.showErrorMessage(`删除用户失败: ${error.message}`);
            }
        }
    }

    /**
     * 导出当前用户数据
     */
    async exportCurrentUser() {
        try {
            const currentUser = this.userManager.getCurrentUser();
            if (!currentUser) {
                this.showErrorMessage('没有当前用户');
                return;
            }

            const exportData = await this.userManager.exportUserData(currentUser.identifier);

            // 创建下载链接
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `瞬光捕手_用户数据_${currentUser.identifier}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);

            this.showSuccessMessage('用户数据导出成功');

        } catch (error) {
            this.showErrorMessage(`导出用户数据失败: ${error.message}`);
        }
    }

    /**
     * 显示导入对话框
     */
    showImportDialog() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (file) {
                this.importUserData(file);
            }
        };
        input.click();
    }

    /**
     * 导入用户数据
     * @param {File} file - 用户数据文件
     */
    async importUserData(file) {
        try {
            const text = await file.text();
            const importData = JSON.parse(text);

            const success = await this.userManager.importUserData(importData, {
                overwrite: confirm('如果用户已存在，是否覆盖现有数据？')
            });

            if (success) {
                this.updateUserList();
                this.showSuccessMessage('用户数据导入成功');
            } else {
                this.showErrorMessage('用户数据导入失败');
            }

        } catch (error) {
            this.showErrorMessage(`导入用户数据失败: ${error.message}`);
        }
    }

    /**
     * 显示数据统计
     */
    async showDataStats() {
        try {
            const currentUser = this.userManager.getCurrentUser();
            if (!currentUser) {
                this.showErrorMessage('没有当前用户');
                return;
            }

            const stats = await this.userManager.getUserStorageUsage(currentUser.identifier);

            if (stats) {
                const statsMessage = `
用户: ${currentUser.displayName} (${currentUser.identifier})
数据键数量: ${stats.totalKeys}
数据总大小: ${(stats.totalSize / 1024).toFixed(2)} KB

数据类型分布:
${stats.keyDetails.map(detail =>
    `- ${detail.dataType}: ${(detail.size / 1024).toFixed(2)} KB`
).join('\n')}
                `.trim();

                alert(statsMessage);
            }

        } catch (error) {
            this.showErrorMessage(`获取数据统计失败: ${error.message}`);
        }
    }

    /**
     * 显示成功消息
     * @param {string} message - 消息内容
     */
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    /**
     * 显示错误消息
     * @param {string} message - 消息内容
     */
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `message-toast ${type}`;
        messageElement.textContent = message;

        // 添加样式
        messageElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            max-width: 300px;
            word-wrap: break-word;
        `;

        // 根据类型设置背景色
        switch (type) {
            case 'success':
                messageElement.style.background = '#4CAF50';
                break;
            case 'error':
                messageElement.style.background = '#f44336';
                break;
            default:
                messageElement.style.background = '#2196F3';
        }

        document.body.appendChild(messageElement);

        // 自动移除
        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => messageElement.remove(), 300);
            }
        }, 3000);
    }
}

// 创建全局用户管理界面实例
if (typeof window !== 'undefined') {
    window.UserManagementUI = UserManagementUI;
    window.userManagementUI = new UserManagementUI();
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserManagementUI;
}
