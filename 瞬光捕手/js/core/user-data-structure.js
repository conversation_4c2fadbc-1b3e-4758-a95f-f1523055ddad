/**
 * 瞬光捕手 - 用户数据结构定义
 * 定义新的用户管理系统的数据结构和规范
 */

/**
 * 用户数据结构规范
 * 
 * 核心设计原则：
 * 1. 用户标识(identifier)和显示名称(display_name)分离
 * 2. 所有用户数据使用用户标识作为前缀进行隔离
 * 3. 向后兼容现有系统
 * 4. 支持用户数据的完整迁移和切换
 */

/**
 * 用户基础数据结构
 */
class UserDataStructure {
    /**
     * 用户核心信息结构
     * @typedef {Object} UserCore
     * @property {string} identifier - 用户唯一标识符（主键，不可变）
     * @property {string} displayName - 用户显示名称（可修改）
     * @property {string} type - 用户类型：'registered'|'guest'|'anonymous'
     * @property {number} createdAt - 创建时间戳
     * @property {number} lastActiveAt - 最后活跃时间戳
     * @property {Object} metadata - 用户元数据
     */
    static getUserCoreStructure() {
        return {
            identifier: '',        // 必填：用户唯一标识符
            displayName: '',       // 必填：用户显示名称
            type: 'registered',    // 用户类型
            createdAt: 0,         // 创建时间
            lastActiveAt: 0,      // 最后活跃时间
            metadata: {
                version: '1.0',    // 数据结构版本
                source: 'manual',  // 创建来源：'manual'|'import'|'migration'
                deviceInfo: null,  // 设备信息
                preferences: {}    // 用户偏好设置
            }
        };
    }

    /**
     * 用户游戏数据结构
     * @typedef {Object} UserGameData
     * @property {Object} progress - 游戏进度
     * @property {Object} stats - 统计数据
     * @property {Object} settings - 游戏设置
     * @property {Object} achievements - 成就数据
     */
    static getUserGameDataStructure() {
        return {
            progress: {
                currentLevel: 1,
                unlockedLevels: [1],
                completedLevels: [],
                customLevels: [],
                totalPlayTime: 0
            },
            stats: {
                totalScore: 0,
                bestScore: 0,
                totalGames: 0,
                perfectHits: 0,
                averageAccuracy: 0,
                levelsCompleted: 0,
                customLevelsCreated: 0
            },
            settings: {
                soundVolume: 50,
                musicVolume: 30,
                language: 'zh-CN',
                difficulty: 'normal',
                theme: 'default',
                controls: {}
            },
            achievements: {
                unlocked: [],
                progress: {},
                notifications: []
            }
        };
    }

    /**
     * 用户凭证数据结构（兼容现有UserCredentialSystem）
     * @typedef {Object} UserCredential
     * @property {string} type - 凭证类型
     * @property {string} identifier - 凭证标识符
     * @property {string} displayName - 显示名称
     * @property {boolean} verified - 是否已验证
     * @property {Object} deviceInfo - 设备信息
     */
    static getUserCredentialStructure() {
        return {
            type: 'username_suffix',  // 凭证类型
            identifier: '',           // 凭证标识符
            displayName: '',          // 显示名称
            fullDisplayName: '',      // 完整显示名称
            verified: false,          // 验证状态
            createdAt: 0,            // 创建时间
            deviceInfo: null,        // 设备信息
            metadata: {}             // 额外元数据
        };
    }
}

/**
 * 存储键值规范
 * 
 * 所有用户相关数据都必须使用用户标识作为前缀
 * 格式：{userIdentifier}_{dataType}[.{subType}]
 */
class StorageKeySchema {
    /**
     * 生成用户数据存储键
     * @param {string} userIdentifier - 用户标识符
     * @param {string} dataType - 数据类型
     * @param {string} subType - 子类型（可选）
     * @returns {string} 存储键
     */
    static generateUserKey(userIdentifier, dataType, subType = null) {
        if (!userIdentifier || !dataType) {
            throw new Error('用户标识符和数据类型不能为空');
        }
        
        // 验证用户标识符格式（只允许字母、数字、下划线、连字符）
        if (!/^[a-zA-Z0-9_-]+$/.test(userIdentifier)) {
            throw new Error('用户标识符格式无效，只允许字母、数字、下划线和连字符');
        }
        
        const baseKey = `${userIdentifier}_${dataType}`;
        return subType ? `${baseKey}.${subType}` : baseKey;
    }

    /**
     * 解析用户数据存储键
     * @param {string} key - 存储键
     * @returns {Object} 解析结果
     */
    static parseUserKey(key) {
        const match = key.match(/^([a-zA-Z0-9_-]+)_([^.]+)(?:\.(.+))?$/);
        if (!match) {
            return null;
        }
        
        return {
            userIdentifier: match[1],
            dataType: match[2],
            subType: match[3] || null,
            isUserKey: true
        };
    }

    /**
     * 获取用户的所有数据键前缀
     * @param {string} userIdentifier - 用户标识符
     * @returns {string} 键前缀
     */
    static getUserKeyPrefix(userIdentifier) {
        return `${userIdentifier}_`;
    }

    /**
     * 预定义的数据类型常量
     */
    static DATA_TYPES = {
        CORE: 'core',                    // 用户核心信息
        GAME_DATA: 'gameData',           // 游戏数据
        SETTINGS: 'settings',            // 用户设置
        PROGRESS: 'progress',            // 游戏进度
        STATS: 'stats',                  // 统计数据
        ACHIEVEMENTS: 'achievements',    // 成就数据
        CREDENTIAL: 'credential',        // 用户凭证
        LEADERBOARD: 'leaderboard',      // 排行榜数据
        CUSTOM_LEVELS: 'customLevels',   // 自定义关卡
        BEHAVIOR: 'behavior',            // 行为数据
        CACHE: 'cache'                   // 缓存数据
    };

    /**
     * 系统级数据键（不需要用户前缀）
     */
    static SYSTEM_KEYS = {
        CURRENT_USER: 'system_currentUser',      // 当前用户标识符
        USER_LIST: 'system_userList',            // 用户列表
        APP_CONFIG: 'system_appConfig',          // 应用配置
        MIGRATION_STATUS: 'system_migrationStatus', // 迁移状态
        LAST_BACKUP: 'system_lastBackup'        // 最后备份时间
    };
}

/**
 * 数据迁移和兼容性处理
 */
class DataMigrationSchema {
    /**
     * 旧数据键到新数据键的映射
     */
    static LEGACY_KEY_MAPPING = {
        'user.credential': (userIdentifier) => 
            StorageKeySchema.generateUserKey(userIdentifier, StorageKeySchema.DATA_TYPES.CREDENTIAL),
        'system.lastPlayer': StorageKeySchema.SYSTEM_KEYS.CURRENT_USER,
        'player.behavior.data': (userIdentifier) => 
            StorageKeySchema.generateUserKey(userIdentifier, StorageKeySchema.DATA_TYPES.BEHAVIOR),
        'level.certification.records': (userIdentifier) => 
            StorageKeySchema.generateUserKey(userIdentifier, StorageKeySchema.DATA_TYPES.CACHE, 'certification')
    };

    /**
     * 玩家数据键模式匹配
     * 匹配格式：player.{playerId}.profile
     */
    static PLAYER_KEY_PATTERN = /^player\.([^.]+)\.profile$/;

    /**
     * 排行榜数据键模式匹配
     * 匹配格式：difficulty_leaderboard.{difficulty}.{category}
     */
    static LEADERBOARD_KEY_PATTERN = /^difficulty_leaderboard\.([^.]+)\.([^.]+)$/;

    /**
     * 检查是否为旧格式的键
     * @param {string} key - 存储键
     * @returns {boolean} 是否为旧格式
     */
    static isLegacyKey(key) {
        return (
            this.LEGACY_KEY_MAPPING.hasOwnProperty(key) ||
            this.PLAYER_KEY_PATTERN.test(key) ||
            this.LEADERBOARD_KEY_PATTERN.test(key)
        );
    }

    /**
     * 将旧格式键转换为新格式键
     * @param {string} legacyKey - 旧格式键
     * @param {string} userIdentifier - 用户标识符
     * @returns {string|null} 新格式键
     */
    static convertLegacyKey(legacyKey, userIdentifier) {
        // 直接映射
        if (this.LEGACY_KEY_MAPPING[legacyKey]) {
            const mapping = this.LEGACY_KEY_MAPPING[legacyKey];
            return typeof mapping === 'function' ? mapping(userIdentifier) : mapping;
        }

        // 玩家数据键
        const playerMatch = legacyKey.match(this.PLAYER_KEY_PATTERN);
        if (playerMatch) {
            return StorageKeySchema.generateUserKey(userIdentifier, StorageKeySchema.DATA_TYPES.CORE);
        }

        // 排行榜数据键
        const leaderboardMatch = legacyKey.match(this.LEADERBOARD_KEY_PATTERN);
        if (leaderboardMatch) {
            const [, difficulty, category] = leaderboardMatch;
            return StorageKeySchema.generateUserKey(
                userIdentifier, 
                StorageKeySchema.DATA_TYPES.LEADERBOARD, 
                `${difficulty}.${category}`
            );
        }

        return null;
    }
}

/**
 * 用户数据验证器
 */
class UserDataValidator {
    /**
     * 验证用户标识符
     * @param {string} identifier - 用户标识符
     * @returns {Object} 验证结果
     */
    static validateIdentifier(identifier) {
        const result = { valid: false, errors: [] };

        if (!identifier || typeof identifier !== 'string') {
            result.errors.push('用户标识符不能为空');
            return result;
        }

        if (identifier.length < 3) {
            result.errors.push('用户标识符长度不能少于3个字符');
        }

        if (identifier.length > 32) {
            result.errors.push('用户标识符长度不能超过32个字符');
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(identifier)) {
            result.errors.push('用户标识符只能包含字母、数字、下划线和连字符');
        }

        // 保留标识符检查
        const reservedIdentifiers = ['system', 'admin', 'guest', 'anonymous', 'test', 'demo'];
        if (reservedIdentifiers.includes(identifier.toLowerCase())) {
            result.errors.push('该标识符为系统保留，请选择其他标识符');
        }

        result.valid = result.errors.length === 0;
        return result;
    }

    /**
     * 验证显示名称
     * @param {string} displayName - 显示名称
     * @returns {Object} 验证结果
     */
    static validateDisplayName(displayName) {
        const result = { valid: false, errors: [] };

        if (!displayName || typeof displayName !== 'string') {
            result.errors.push('显示名称不能为空');
            return result;
        }

        const trimmedName = displayName.trim();
        if (trimmedName.length === 0) {
            result.errors.push('显示名称不能为空白字符');
        }

        if (trimmedName.length > 50) {
            result.errors.push('显示名称长度不能超过50个字符');
        }

        // 检查是否包含特殊字符（允许中文、字母、数字、空格、常见标点）
        if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_.,!?()（）]+$/.test(trimmedName)) {
            result.errors.push('显示名称包含不允许的特殊字符');
        }

        result.valid = result.errors.length === 0;
        return result;
    }

    /**
     * 验证用户核心数据结构
     * @param {Object} userData - 用户数据
     * @returns {Object} 验证结果
     */
    static validateUserCore(userData) {
        const result = { valid: false, errors: [] };

        if (!userData || typeof userData !== 'object') {
            result.errors.push('用户数据必须是对象类型');
            return result;
        }

        // 验证必填字段
        const requiredFields = ['identifier', 'displayName', 'type', 'createdAt'];
        for (const field of requiredFields) {
            if (!userData.hasOwnProperty(field)) {
                result.errors.push(`缺少必填字段: ${field}`);
            }
        }

        // 验证标识符
        if (userData.identifier) {
            const identifierValidation = this.validateIdentifier(userData.identifier);
            if (!identifierValidation.valid) {
                result.errors.push(...identifierValidation.errors);
            }
        }

        // 验证显示名称
        if (userData.displayName) {
            const displayNameValidation = this.validateDisplayName(userData.displayName);
            if (!displayNameValidation.valid) {
                result.errors.push(...displayNameValidation.errors);
            }
        }

        // 验证用户类型
        const validTypes = ['registered', 'guest', 'anonymous'];
        if (userData.type && !validTypes.includes(userData.type)) {
            result.errors.push(`无效的用户类型: ${userData.type}`);
        }

        result.valid = result.errors.length === 0;
        return result;
    }
}

// 导出所有类
if (typeof window !== 'undefined') {
    window.UserDataStructure = UserDataStructure;
    window.StorageKeySchema = StorageKeySchema;
    window.DataMigrationSchema = DataMigrationSchema;
    window.UserDataValidator = UserDataValidator;
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UserDataStructure,
        StorageKeySchema,
        DataMigrationSchema,
        UserDataValidator
    };
}
