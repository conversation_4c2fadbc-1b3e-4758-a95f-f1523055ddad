/**
 * 瞬光捕手 - 用户管理核心类
 * 实现新的用户配置管理系统，支持用户标识和显示名称分离
 */

class UserManager {
    constructor() {
        this.initialized = false;
        this.currentUser = null;
        this.users = new Map(); // 用户列表缓存
        this.storageService = null;
        
        // 事件监听器
        this.eventListeners = new Map();
        
        console.log('👤 用户管理器已创建');
    }

    /**
     * 初始化用户管理器
     */
    async init() {
        try {
            console.log('🔧 初始化用户管理器...');
            
            // 确保存储服务可用
            if (!window.storageService) {
                throw new Error('存储服务不可用');
            }
            this.storageService = window.storageService;
            
            // 加载用户列表
            await this.loadUserList();
            
            // 加载当前用户
            await this.loadCurrentUser();
            
            // 如果没有当前用户，创建默认游客用户
            if (!this.currentUser) {
                await this.createGuestUser();
            }
            
            this.initialized = true;
            console.log('✅ 用户管理器初始化完成');
            
            // 触发初始化完成事件
            this.dispatchEvent('initialized', { currentUser: this.currentUser });
            
        } catch (error) {
            console.error('❌ 用户管理器初始化失败:', error);
            this.initialized = true; // 即使失败也标记为已初始化，避免重复初始化
            throw error;
        }
    }

    /**
     * 创建新用户
     * @param {string} identifier - 用户标识符
     * @param {string} displayName - 显示名称
     * @param {Object} options - 创建选项
     * @returns {Promise<Object>} 创建的用户对象
     */
    async createUser(identifier, displayName, options = {}) {
        console.log(`👤 创建用户: ${identifier} (${displayName})`);
        
        // 验证输入参数
        const identifierValidation = UserDataValidator.validateIdentifier(identifier);
        if (!identifierValidation.valid) {
            throw new Error(`用户标识符无效: ${identifierValidation.errors.join(', ')}`);
        }
        
        const displayNameValidation = UserDataValidator.validateDisplayName(displayName);
        if (!displayNameValidation.valid) {
            throw new Error(`显示名称无效: ${displayNameValidation.errors.join(', ')}`);
        }
        
        // 检查标识符是否已存在
        if (await this.userExists(identifier)) {
            throw new Error(`用户标识符已存在: ${identifier}`);
        }
        
        // 检查显示名称是否已被使用（可选检查）
        if (options.checkDisplayNameUnique && await this.displayNameExists(displayName)) {
            throw new Error(`显示名称已被使用: ${displayName}`);
        }
        
        // 创建用户核心数据
        const userCore = {
            ...UserDataStructure.getUserCoreStructure(),
            identifier: identifier,
            displayName: displayName.trim(),
            type: options.type || 'registered',
            createdAt: Date.now(),
            lastActiveAt: Date.now(),
            metadata: {
                version: '1.0',
                source: options.source || 'manual',
                deviceInfo: this.getDeviceInfo(),
                preferences: options.preferences || {}
            }
        };
        
        // 验证用户数据结构
        const validation = UserDataValidator.validateUserCore(userCore);
        if (!validation.valid) {
            throw new Error(`用户数据验证失败: ${validation.errors.join(', ')}`);
        }
        
        // 创建用户游戏数据
        const gameData = UserDataStructure.getUserGameDataStructure();
        
        try {
            // 保存用户核心数据
            const coreKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.CORE);
            await this.storageService.put(coreKey, userCore);
            
            // 保存用户游戏数据
            const gameDataKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.GAME_DATA);
            await this.storageService.put(gameDataKey, gameData);
            
            // 更新用户列表
            this.users.set(identifier, userCore);
            await this.saveUserList();
            
            console.log(`✅ 用户创建成功: ${identifier}`);
            
            // 触发用户创建事件
            this.dispatchEvent('userCreated', { user: userCore });
            
            return userCore;
            
        } catch (error) {
            console.error(`❌ 用户创建失败: ${identifier}`, error);
            throw new Error(`用户创建失败: ${error.message}`);
        }
    }

    /**
     * 切换到指定用户
     * @param {string} identifier - 用户标识符
     * @returns {Promise<boolean>} 是否成功切换
     */
    async switchToUser(identifier) {
        console.log(`🔄 切换到用户: ${identifier}`);
        
        try {
            // 检查用户是否存在
            if (!await this.userExists(identifier)) {
                throw new Error(`用户不存在: ${identifier}`);
            }
            
            // 保存当前用户的最后活跃时间
            if (this.currentUser && this.currentUser.identifier !== identifier) {
                await this.updateUserLastActive(this.currentUser.identifier);
            }
            
            // 加载目标用户数据
            const targetUser = await this.loadUserCore(identifier);
            if (!targetUser) {
                throw new Error(`无法加载用户数据: ${identifier}`);
            }
            
            // 更新当前用户
            const previousUser = this.currentUser;
            this.currentUser = targetUser;
            
            // 更新最后活跃时间
            await this.updateUserLastActive(identifier);
            
            // 保存当前用户设置
            await this.storageService.put(StorageKeySchema.SYSTEM_KEYS.CURRENT_USER, identifier);
            
            console.log(`✅ 用户切换成功: ${targetUser.displayName} (${identifier})`);
            
            // 触发用户切换事件
            this.dispatchEvent('userSwitched', { 
                currentUser: this.currentUser, 
                previousUser: previousUser 
            });
            
            return true;
            
        } catch (error) {
            console.error(`❌ 用户切换失败: ${identifier}`, error);
            return false;
        }
    }

    /**
     * 删除用户
     * @param {string} identifier - 用户标识符
     * @param {Object} options - 删除选项
     * @returns {Promise<boolean>} 是否成功删除
     */
    async deleteUser(identifier, options = {}) {
        console.log(`🗑️ 删除用户: ${identifier}`);
        
        try {
            // 检查用户是否存在
            if (!await this.userExists(identifier)) {
                console.warn(`用户不存在，跳过删除: ${identifier}`);
                return true;
            }
            
            // 不能删除当前用户（除非指定强制删除）
            if (this.currentUser && this.currentUser.identifier === identifier && !options.force) {
                throw new Error('不能删除当前用户，请先切换到其他用户');
            }
            
            // 获取用户数据键前缀
            const keyPrefix = StorageKeySchema.getUserKeyPrefix(identifier);
            
            // 获取所有用户相关的键
            const userKeys = await this.storageService.list(keyPrefix);
            
            // 删除所有用户数据
            for (const key of userKeys) {
                await this.storageService.delete(key);
            }
            
            // 从用户列表中移除
            this.users.delete(identifier);
            await this.saveUserList();
            
            // 如果删除的是当前用户，切换到游客用户
            if (this.currentUser && this.currentUser.identifier === identifier) {
                await this.createGuestUser();
                await this.switchToUser('guest');
            }
            
            console.log(`✅ 用户删除成功: ${identifier}`);
            
            // 触发用户删除事件
            this.dispatchEvent('userDeleted', { identifier });
            
            return true;
            
        } catch (error) {
            console.error(`❌ 用户删除失败: ${identifier}`, error);
            return false;
        }
    }

    /**
     * 获取当前用户
     * @returns {Object|null} 当前用户对象
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 获取所有用户列表
     * @returns {Array} 用户列表
     */
    getAllUsers() {
        return Array.from(this.users.values()).sort((a, b) => b.lastActiveAt - a.lastActiveAt);
    }

    /**
     * 检查用户是否存在
     * @param {string} identifier - 用户标识符
     * @returns {Promise<boolean>} 是否存在
     */
    async userExists(identifier) {
        // 先检查内存缓存
        if (this.users.has(identifier)) {
            return true;
        }
        
        // 检查存储中是否存在
        const coreKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.CORE);
        const userData = await this.storageService.get(coreKey);
        return userData !== null;
    }

    /**
     * 检查显示名称是否已存在
     * @param {string} displayName - 显示名称
     * @returns {Promise<boolean>} 是否存在
     */
    async displayNameExists(displayName) {
        const trimmedName = displayName.trim().toLowerCase();
        
        for (const user of this.users.values()) {
            if (user.displayName.toLowerCase() === trimmedName) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 更新用户显示名称
     * @param {string} identifier - 用户标识符
     * @param {string} newDisplayName - 新显示名称
     * @returns {Promise<boolean>} 是否成功更新
     */
    async updateUserDisplayName(identifier, newDisplayName) {
        try {
            // 验证新显示名称
            const validation = UserDataValidator.validateDisplayName(newDisplayName);
            if (!validation.valid) {
                throw new Error(`显示名称无效: ${validation.errors.join(', ')}`);
            }
            
            // 加载用户数据
            const userData = await this.loadUserCore(identifier);
            if (!userData) {
                throw new Error(`用户不存在: ${identifier}`);
            }
            
            // 更新显示名称
            userData.displayName = newDisplayName.trim();
            userData.lastActiveAt = Date.now();
            
            // 保存更新后的数据
            const coreKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.CORE);
            await this.storageService.put(coreKey, userData);
            
            // 更新内存缓存
            this.users.set(identifier, userData);
            
            // 如果是当前用户，更新当前用户对象
            if (this.currentUser && this.currentUser.identifier === identifier) {
                this.currentUser = userData;
            }
            
            console.log(`✅ 用户显示名称更新成功: ${identifier} -> ${newDisplayName}`);
            
            // 触发用户更新事件
            this.dispatchEvent('userUpdated', { user: userData });
            
            return true;
            
        } catch (error) {
            console.error(`❌ 用户显示名称更新失败: ${identifier}`, error);
            return false;
        }
    }

    /**
     * 创建游客用户
     * @returns {Promise<Object>} 游客用户对象
     */
    async createGuestUser() {
        const guestIdentifier = 'guest';
        const guestDisplayName = '游客';

        // 检查游客用户是否已存在
        if (await this.userExists(guestIdentifier)) {
            return await this.loadUserCore(guestIdentifier);
        }

        console.log('👤 创建游客用户');

        return await this.createUser(guestIdentifier, guestDisplayName, {
            type: 'guest',
            source: 'system',
            checkDisplayNameUnique: false
        });
    }

    /**
     * 加载用户列表
     */
    async loadUserList() {
        try {
            const userListData = await this.storageService.get(StorageKeySchema.SYSTEM_KEYS.USER_LIST);

            if (userListData && Array.isArray(userListData)) {
                // 加载每个用户的核心数据
                for (const identifier of userListData) {
                    const userData = await this.loadUserCore(identifier);
                    if (userData) {
                        this.users.set(identifier, userData);
                    }
                }
                console.log(`📋 加载了 ${this.users.size} 个用户`);
            }
        } catch (error) {
            console.error('❌ 加载用户列表失败:', error);
        }
    }

    /**
     * 保存用户列表
     */
    async saveUserList() {
        try {
            const userList = Array.from(this.users.keys());
            await this.storageService.put(StorageKeySchema.SYSTEM_KEYS.USER_LIST, userList);
        } catch (error) {
            console.error('❌ 保存用户列表失败:', error);
        }
    }

    /**
     * 加载当前用户
     */
    async loadCurrentUser() {
        try {
            const currentUserIdentifier = await this.storageService.get(StorageKeySchema.SYSTEM_KEYS.CURRENT_USER);

            if (currentUserIdentifier && await this.userExists(currentUserIdentifier)) {
                this.currentUser = await this.loadUserCore(currentUserIdentifier);
                console.log(`👤 加载当前用户: ${this.currentUser.displayName} (${currentUserIdentifier})`);
            }
        } catch (error) {
            console.error('❌ 加载当前用户失败:', error);
        }
    }

    /**
     * 加载用户核心数据
     * @param {string} identifier - 用户标识符
     * @returns {Promise<Object|null>} 用户核心数据
     */
    async loadUserCore(identifier) {
        try {
            const coreKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.CORE);
            const userData = await this.storageService.get(coreKey);

            if (userData) {
                // 验证数据结构
                const validation = UserDataValidator.validateUserCore(userData);
                if (validation.valid) {
                    return userData;
                } else {
                    console.warn(`⚠️ 用户数据结构无效: ${identifier}`, validation.errors);
                }
            }

            return null;
        } catch (error) {
            console.error(`❌ 加载用户核心数据失败: ${identifier}`, error);
            return null;
        }
    }

    /**
     * 更新用户最后活跃时间
     * @param {string} identifier - 用户标识符
     */
    async updateUserLastActive(identifier) {
        try {
            const userData = await this.loadUserCore(identifier);
            if (userData) {
                userData.lastActiveAt = Date.now();

                const coreKey = StorageKeySchema.generateUserKey(identifier, StorageKeySchema.DATA_TYPES.CORE);
                await this.storageService.put(coreKey, userData);

                // 更新内存缓存
                this.users.set(identifier, userData);
            }
        } catch (error) {
            console.error(`❌ 更新用户活跃时间失败: ${identifier}`, error);
        }
    }

    /**
     * 获取设备信息
     * @returns {Object} 设备信息
     */
    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            screen: {
                width: screen.width,
                height: screen.height
            },
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timestamp: Date.now()
        };
    }

    /**
     * 获取用户的存储使用情况
     * @param {string} identifier - 用户标识符
     * @returns {Promise<Object>} 存储使用情况
     */
    async getUserStorageUsage(identifier) {
        try {
            const keyPrefix = StorageKeySchema.getUserKeyPrefix(identifier);
            const userKeys = await this.storageService.list(keyPrefix);

            let totalSize = 0;
            const keyDetails = [];

            for (const key of userKeys) {
                const data = await this.storageService.get(key);
                const size = JSON.stringify(data).length;
                totalSize += size;

                keyDetails.push({
                    key: key,
                    size: size,
                    dataType: StorageKeySchema.parseUserKey(key)?.dataType || 'unknown'
                });
            }

            return {
                identifier: identifier,
                totalKeys: userKeys.length,
                totalSize: totalSize,
                keyDetails: keyDetails.sort((a, b) => b.size - a.size)
            };

        } catch (error) {
            console.error(`❌ 获取用户存储使用情况失败: ${identifier}`, error);
            return null;
        }
    }

    /**
     * 导出用户数据
     * @param {string} identifier - 用户标识符
     * @returns {Promise<Object>} 用户数据导出
     */
    async exportUserData(identifier) {
        try {
            const keyPrefix = StorageKeySchema.getUserKeyPrefix(identifier);
            const userKeys = await this.storageService.list(keyPrefix);

            const exportData = {
                metadata: {
                    identifier: identifier,
                    exportedAt: Date.now(),
                    version: '1.0',
                    keyCount: userKeys.length
                },
                data: {}
            };

            for (const key of userKeys) {
                const data = await this.storageService.get(key);
                exportData.data[key] = data;
            }

            console.log(`📤 用户数据导出完成: ${identifier}`);
            return exportData;

        } catch (error) {
            console.error(`❌ 用户数据导出失败: ${identifier}`, error);
            throw error;
        }
    }

    /**
     * 导入用户数据
     * @param {Object} exportData - 导出的用户数据
     * @param {Object} options - 导入选项
     * @returns {Promise<boolean>} 是否成功导入
     */
    async importUserData(exportData, options = {}) {
        try {
            if (!exportData || !exportData.metadata || !exportData.data) {
                throw new Error('导入数据格式无效');
            }

            const identifier = exportData.metadata.identifier;
            console.log(`📥 导入用户数据: ${identifier}`);

            // 检查是否覆盖现有用户
            if (await this.userExists(identifier) && !options.overwrite) {
                throw new Error(`用户已存在: ${identifier}，请设置 overwrite: true 以覆盖`);
            }

            // 导入所有数据
            for (const [key, data] of Object.entries(exportData.data)) {
                await this.storageService.put(key, data);
            }

            // 重新加载用户列表
            await this.loadUserList();

            console.log(`✅ 用户数据导入完成: ${identifier}`);

            // 触发用户导入事件
            this.dispatchEvent('userImported', { identifier, exportData });

            return true;

        } catch (error) {
            console.error('❌ 用户数据导入失败:', error);
            return false;
        }
    }

    /**
     * 添加事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 监听器函数
     */
    addEventListener(eventType, listener) {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, []);
        }
        this.eventListeners.get(eventType).push(listener);
    }

    /**
     * 移除事件监听器
     * @param {string} eventType - 事件类型
     * @param {Function} listener - 监听器函数
     */
    removeEventListener(eventType, listener) {
        if (this.eventListeners.has(eventType)) {
            const listeners = this.eventListeners.get(eventType);
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    dispatchEvent(eventType, eventData = {}) {
        if (this.eventListeners.has(eventType)) {
            const listeners = this.eventListeners.get(eventType);
            for (const listener of listeners) {
                try {
                    listener(eventData);
                } catch (error) {
                    console.error(`❌ 事件监听器执行失败 [${eventType}]:`, error);
                }
            }
        }

        // 同时触发全局事件（如果在浏览器环境中）
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const customEvent = new CustomEvent(`userManager:${eventType}`, {
                detail: eventData
            });
            window.dispatchEvent(customEvent);
        }
    }
}

// 创建全局用户管理器实例
if (typeof window !== 'undefined') {
    window.UserManager = UserManager;
    window.userManager = new UserManager();
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserManager;
}
